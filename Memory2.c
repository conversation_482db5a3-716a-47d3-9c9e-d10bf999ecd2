//
// 内存操作辅助函数实现 (续)
// MyDriver - KMDF 进程内存读写驱动
//

#include "Memory.h"

#ifdef ALLOC_PRAGMA
#pragma alloc_text (PAGE, CheckMemoryPermissions)
#pragma alloc_text (PAGE, IsSystemProcess)
#pragma alloc_text (PAGE, IsProtectedProcess)
#endif

//
// 安全的内存复制
//
NTSTATUS
SafeMemoryCopy(
    _In_ PEPROCESS TargetProcess,
    _In_ PVOID TargetAddress,
    _In_ PVOID SourceAddress,
    _In_ SIZE_T Size,
    _In_ BOOLEAN IsWrite,
    _Out_opt_ PSIZE_T BytesTransferred
)
{
    NTSTATUS status = STATUS_SUCCESS;
    SIZE_T bytesTransferred = 0;
    SIZE_T remainingBytes = Size;
    PUCHAR targetPtr = (PUCHAR)TargetAddress;
    PUCHAR sourcePtr = (PUCHAR)SourceAddress;
    SIZE_T chunkSize;

    UNREFERENCED_PARAMETER(TargetProcess);

    if (BytesTransferred != NULL) {
        *BytesTransferred = 0;
    }

    __try {
        //
        // 分块复制以提高安全性
        //
        while (remainingBytes > 0) {
            chunkSize = min(remainingBytes, PAGE_SIZE);

            //
            // 验证内存页是否有效
            //
            if (IsWrite) {
                if (!MmIsAddressValid(targetPtr)) {
                    ErrorPrint("SafeMemoryCopy: 目标地址无效 0x%p", targetPtr);
                    status = STATUS_INVALID_ADDRESS;
                    break;
                }
                
                //
                // 执行写入操作
                //
                RtlCopyMemory(targetPtr, sourcePtr, chunkSize);
            } else {
                if (!MmIsAddressValid(sourcePtr)) {
                    ErrorPrint("SafeMemoryCopy: 源地址无效 0x%p", sourcePtr);
                    status = STATUS_INVALID_ADDRESS;
                    break;
                }
                
                //
                // 执行读取操作
                //
                RtlCopyMemory(targetPtr, sourcePtr, chunkSize);
            }

            bytesTransferred += chunkSize;
            remainingBytes -= chunkSize;
            targetPtr += chunkSize;
            sourcePtr += chunkSize;
        }

    } __except(EXCEPTION_EXECUTE_HANDLER) {
        status = GetExceptionCode();
        ErrorPrint("SafeMemoryCopy: 内存复制异常，状态码: 0x%08X", status);
    }

    if (BytesTransferred != NULL) {
        *BytesTransferred = bytesTransferred;
    }

    return status;
}

//
// 检查内存区域权限
//
NTSTATUS
CheckMemoryPermissions(
    _In_ PEPROCESS Process,
    _In_ PVOID BaseAddress,
    _In_ SIZE_T Size,
    _In_ BOOLEAN RequireWrite
)
{
    PAGED_CODE();

    UNREFERENCED_PARAMETER(Process);
    UNREFERENCED_PARAMETER(BaseAddress);
    UNREFERENCED_PARAMETER(Size);
    UNREFERENCED_PARAMETER(RequireWrite);

    //
    // 这里可以实现更详细的内存权限检查
    // 例如检查页面保护属性、DEP 状态等
    //
    
    DebugPrint("CheckMemoryPermissions: 权限检查通过");
    return STATUS_SUCCESS;
}

//
// 验证用户模式地址
//
BOOLEAN
IsValidUserModeAddress(
    _In_ PVOID Address,
    _In_ SIZE_T Size
)
{
    ULONG_PTR startAddr = (ULONG_PTR)Address;
    ULONG_PTR endAddr = startAddr + Size - 1;

    //
    // 检查地址是否在用户模式范围内
    //
    if (startAddr < MIN_USER_ADDRESS) {
        return FALSE;
    }

    //
    // 检查是否超出用户模式地址空间
    //
#ifdef _WIN64
    if (endAddr >= 0x7FFFFFFFFFFF) {  // 64位系统用户空间上限
        return FALSE;
    }
#else
    if (endAddr >= 0x80000000) {      // 32位系统用户空间上限
        return FALSE;
    }
#endif

    //
    // 检查地址范围是否有效
    //
    if (endAddr < startAddr) {
        return FALSE;
    }

    return TRUE;
}

//
// 检查是否为系统进程
//
BOOLEAN
IsSystemProcess(
    _In_ PEPROCESS Process
)
{
    ULONG processId;

    PAGED_CODE();

    //
    // 获取进程 ID
    //
    processId = (ULONG)(ULONG_PTR)PsGetProcessId(Process);

    //
    // 系统进程 (PID 4) 和空闲进程 (PID 0)
    //
    if (processId == 0 || processId == 4) {
        return TRUE;
    }

    return FALSE;
}

//
// 检查是否为受保护进程
//
BOOLEAN
IsProtectedProcess(
    _In_ PEPROCESS Process
)
{
    PUNICODE_STRING processName;
    ULONG i;

    PAGED_CODE();

    //
    // 首先检查是否为系统进程
    //
    if (IsSystemProcess(Process)) {
        return TRUE;
    }

    //
    // 获取进程名称
    //
    processName = (PUNICODE_STRING)((PUCHAR)Process + 0x450); // 这个偏移可能需要根据系统版本调整

    if (processName == NULL || processName->Buffer == NULL) {
        return FALSE;
    }

    //
    // 检查是否在受保护进程列表中
    //
    for (i = 0; i < ProtectedProcessCount; i++) {
        if (wcsstr(processName->Buffer, ProtectedProcessNames[i]) != NULL) {
            InfoPrint("IsProtectedProcess: 检测到受保护进程: %wZ", processName);
            return TRUE;
        }
    }

    return FALSE;
}

//
// 更新读取统计
//
VOID
UpdateReadStats(
    _In_ SIZE_T BytesRead,
    _In_ BOOLEAN Success
)
{
    KIRQL oldIrql;
    LARGE_INTEGER currentTime;

    KeQuerySystemTime(&currentTime);

    KeAcquireSpinLock(&g_MemoryStatsLock, &oldIrql);

    g_MemoryStats.TotalReadOperations++;
    g_MemoryStats.LastOperationTime = currentTime;

    if (Success) {
        g_MemoryStats.TotalBytesRead += BytesRead;
    } else {
        g_MemoryStats.FailedOperations++;
    }

    KeReleaseSpinLock(&g_MemoryStatsLock, oldIrql);
}

//
// 更新写入统计
//
VOID
UpdateWriteStats(
    _In_ SIZE_T BytesWritten,
    _In_ BOOLEAN Success
)
{
    KIRQL oldIrql;
    LARGE_INTEGER currentTime;

    KeQuerySystemTime(&currentTime);

    KeAcquireSpinLock(&g_MemoryStatsLock, &oldIrql);

    g_MemoryStats.TotalWriteOperations++;
    g_MemoryStats.LastOperationTime = currentTime;

    if (Success) {
        g_MemoryStats.TotalBytesWritten += BytesWritten;
    } else {
        g_MemoryStats.FailedOperations++;
    }

    KeReleaseSpinLock(&g_MemoryStatsLock, oldIrql);
}

//
// 获取内存统计
//
VOID
GetMemoryStats(
    _Out_ PMEMORY_OPERATION_STATS Stats
)
{
    KIRQL oldIrql;

    if (Stats == NULL) {
        return;
    }

    KeAcquireSpinLock(&g_MemoryStatsLock, &oldIrql);
    RtlCopyMemory(Stats, &g_MemoryStats, sizeof(MEMORY_OPERATION_STATS));
    KeReleaseSpinLock(&g_MemoryStatsLock, oldIrql);
}
