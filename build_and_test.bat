@echo off
echo ===== MyDriver 编译和测试脚本 =====

REM 设置 Visual Studio 环境
call "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat" 2>nul
if errorlevel 1 (
    call "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Enterprise\Common7\Tools\VsDevCmd.bat" 2>nul
    if errorlevel 1 (
        call "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat" 2>nul
        if errorlevel 1 (
            echo 错误: 无法找到 Visual Studio 2019
            pause
            exit /b 1
        )
    )
)

echo 找到 Visual Studio 环境

REM 编译测试程序
echo.
echo 编译用户模式测试程序...
cl /EHsc TestApp.cpp /Fe:TestApp.exe
if errorlevel 1 (
    echo 编译测试程序失败
    pause
    exit /b 1
)

echo 测试程序编译成功

REM 显示项目文件结构
echo.
echo 当前项目文件:
dir /b *.h *.c *.cpp

echo.
echo 注意: 
echo 1. 驱动源代码已创建完成
echo 2. 需要在 Visual Studio 中手动添加源文件到项目
echo 3. 需要管理员权限来安装和测试驱动
echo 4. 建议使用 Windows Driver Kit (WDK) 进行完整编译

echo.
echo 源代码文件说明:
echo - Common.h     : 公共定义和数据结构
echo - Driver.h/c   : 驱动入口点和主要逻辑
echo - Device.h/c   : 设备管理和 I/O 处理
echo - Memory.h/c   : 内存读写核心功能
echo - Memory2.c    : 内存操作辅助函数
echo - TestApp.cpp  : 用户模式测试程序

pause
