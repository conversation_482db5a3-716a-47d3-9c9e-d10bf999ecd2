//
// 用户模式测试程序
// MyDriver - KMDF 进程内存读写驱动测试
//

#include <windows.h>
#include <iostream>
#include <iomanip>
#include <vector>
#include "Common.h"

using namespace std;

class DriverTester {
private:
    HANDLE hDevice;

public:
    DriverTester() : hDevice(INVALID_HANDLE_VALUE) {}
    
    ~DriverTester() {
        if (hDevice != INVALID_HANDLE_VALUE) {
            CloseHandle(hDevice);
        }
    }

    bool OpenDriver() {
        hDevice = CreateFile(
            USER_DEVICE_NAME,
            GENERIC_READ | GENERIC_WRITE,
            0,
            NULL,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            NULL
        );

        if (hDevice == INVALID_HANDLE_VALUE) {
            wcout << L"无法打开驱动设备，错误码: " << GetLastError() << endl;
            return false;
        }

        wcout << L"成功打开驱动设备" << endl;
        return true;
    }

    bool TestReadMemory(ULONG processId, PVOID address, SIZE_T size) {
        MEMORY_READ_REQUEST request = {0};
        request.ProcessId = processId;
        request.BaseAddress = address;
        request.Size = size;

        vector<BYTE> buffer(size);
        DWORD bytesReturned = 0;

        BOOL result = DeviceIoControl(
            hDevice,
            IOCTL_READ_PROCESS_MEMORY,
            &request,
            sizeof(request),
            buffer.data(),
            (DWORD)size,
            &bytesReturned,
            NULL
        );

        if (result) {
            wcout << L"成功读取 " << bytesReturned << L" 字节" << endl;
            
            // 显示前16字节的十六进制数据
            wcout << L"数据内容: ";
            for (DWORD i = 0; i < min(bytesReturned, 16UL); i++) {
                wcout << hex << setw(2) << setfill(L'0') << buffer[i] << L" ";
            }
            wcout << endl;
            return true;
        } else {
            wcout << L"读取内存失败，错误码: " << GetLastError() << endl;
            return false;
        }
    }

    bool TestWriteMemory(ULONG processId, PVOID address, const vector<BYTE>& data) {
        SIZE_T requestSize = sizeof(MEMORY_WRITE_REQUEST) + data.size();
        vector<BYTE> requestBuffer(requestSize);
        
        PMEMORY_WRITE_REQUEST request = (PMEMORY_WRITE_REQUEST)requestBuffer.data();
        request->ProcessId = processId;
        request->BaseAddress = address;
        request->Size = data.size();
        
        // 复制数据到请求缓冲区
        memcpy(requestBuffer.data() + sizeof(MEMORY_WRITE_REQUEST), 
               data.data(), data.size());

        DWORD bytesReturned = 0;

        BOOL result = DeviceIoControl(
            hDevice,
            IOCTL_WRITE_PROCESS_MEMORY,
            requestBuffer.data(),
            (DWORD)requestSize,
            NULL,
            0,
            &bytesReturned,
            NULL
        );

        if (result) {
            wcout << L"成功写入 " << bytesReturned << L" 字节" << endl;
            return true;
        } else {
            wcout << L"写入内存失败，错误码: " << GetLastError() << endl;
            return false;
        }
    }

    void RunTests() {
        wcout << L"=== MyDriver 测试程序 ===" << endl;
        
        if (!OpenDriver()) {
            return;
        }

        // 获取当前进程ID进行测试
        ULONG currentPid = GetCurrentProcessId();
        wcout << L"当前进程 ID: " << currentPid << endl;

        // 测试读取当前进程的内存
        PVOID testAddress = (PVOID)0x400000; // 典型的可执行文件基址
        wcout << L"\n测试读取内存..." << endl;
        TestReadMemory(currentPid, testAddress, 64);

        // 测试写入内存（注意：这可能会失败，因为代码段通常是只读的）
        wcout << L"\n测试写入内存..." << endl;
        vector<BYTE> testData = {0x90, 0x90, 0x90, 0x90}; // NOP 指令
        TestWriteMemory(currentPid, testAddress, testData);

        wcout << L"\n测试完成" << endl;
    }
};

int main() {
    // 设置控制台输出为 UTF-8
    SetConsoleOutputCP(CP_UTF8);
    
    DriverTester tester;
    tester.RunTests();

    wcout << L"\n按任意键退出..." << endl;
    _getch();
    return 0;
}
