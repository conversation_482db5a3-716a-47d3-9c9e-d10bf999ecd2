#pragma once

//
// 公共头文件 - 用户模式和内核模式共享的定义
// MyDriver - KMDF 进程内存读写驱动
//

#ifndef _COMMON_H_
#define _COMMON_H_

//
// 设备名称和符号链接定义
//
#define DEVICE_NAME             L"\\Device\\MyDriver"
#define SYMBOLIC_LINK_NAME      L"\\DosDevices\\MyDriver"
#define USER_DEVICE_NAME        L"\\\\.\\MyDriver"

//
// IOCTL 命令码定义
// 使用 CTL_CODE 宏生成标准命令码
//
#define IOCTL_READ_PROCESS_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_WRITE_PROCESS_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)

//
// 数据结构定义
//

// 内存读取请求结构
typedef struct _MEMORY_READ_REQUEST {
    ULONG ProcessId;        // 目标进程 ID
    PVOID BaseAddress;      // 读取的基地址
    SIZE_T Size;           // 读取大小
} MEMORY_READ_REQUEST, *PMEMORY_READ_REQUEST;

// 内存写入请求结构
typedef struct _MEMORY_WRITE_REQUEST {
    ULONG ProcessId;        // 目标进程 ID
    PVOID BaseAddress;      // 写入的基地址
    SIZE_T Size;           // 写入大小
    // 数据紧跟在结构后面
} MEMORY_WRITE_REQUEST, *PMEMORY_WRITE_REQUEST;

//
// 错误码和状态常量
//
#define MYDRIVER_SUCCESS                    0x00000000
#define MYDRIVER_ERROR_INVALID_PARAMETER    0xE0000001
#define MYDRIVER_ERROR_PROCESS_NOT_FOUND    0xE0000002
#define MYDRIVER_ERROR_ACCESS_DENIED        0xE0000003
#define MYDRIVER_ERROR_INVALID_ADDRESS      0xE0000004
#define MYDRIVER_ERROR_BUFFER_TOO_SMALL     0xE0000005
#define MYDRIVER_ERROR_MEMORY_ACCESS        0xE0000006

//
// 配置常量
//
#define MAX_MEMORY_SIZE                     (1024 * 1024)  // 最大单次操作 1MB
#define MIN_MEMORY_SIZE                     1               // 最小操作大小
#define INVALID_PROCESS_ID                  0               // 无效进程 ID

#endif // _COMMON_H_
