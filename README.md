# MyDriver - KMDF 进程内存读写驱动

## 项目概述

这是一个基于 Windows Kernel-Mode Driver Framework (KMDF) 的内核驱动程序，实现了进程内存读写功能。驱动程序提供了安全的 IOCTL 接口，允许用户模式应用程序读取和写入指定进程的虚拟内存。

## 功能特性

### 核心功能
- ✅ 进程内存读取 - 读取指定进程的虚拟内存内容
- ✅ 进程内存写入 - 向指定进程的虚拟内存写入数据
- ✅ KMDF 框架 - 使用现代 KMDF 编程模型
- ✅ 安全验证 - 完整的参数验证和权限检查
- ✅ 错误处理 - 健壮的异常处理机制

### 安全特性
- 🛡️ 进程保护 - 防止访问系统关键进程
- 🛡️ 地址验证 - 验证内存地址有效性和权限
- 🛡️ 大小限制 - 限制单次操作的最大内存大小
- 🛡️ 异常处理 - 使用 SEH 保护内存访问操作

## 文件结构

```
MyDriver/
├── Common.h          # 公共定义和数据结构
├── Driver.h          # 驱动主头文件
├── Driver.c          # 驱动入口点实现
├── Device.h          # 设备管理头文件
├── Device.c          # 设备和 I/O 队列实现
├── Memory.h          # 内存操作头文件
├── Memory.c          # 内存读写核心功能
├── Memory2.c         # 内存操作辅助函数
├── TestApp.cpp       # 用户模式测试程序
├── build_and_test.bat # 编译测试脚本
└── README.md         # 本文档
```

## IOCTL 接口

### 内存读取
- **命令码**: `IOCTL_READ_PROCESS_MEMORY`
- **输入**: `MEMORY_READ_REQUEST` 结构
- **输出**: 读取的内存数据

```c
typedef struct _MEMORY_READ_REQUEST {
    ULONG ProcessId;        // 目标进程 ID
    PVOID BaseAddress;      // 读取的基地址
    SIZE_T Size;           // 读取大小
} MEMORY_READ_REQUEST;
```

### 内存写入
- **命令码**: `IOCTL_WRITE_PROCESS_MEMORY`
- **输入**: `MEMORY_WRITE_REQUEST` 结构 + 数据
- **输出**: 写入的字节数

```c
typedef struct _MEMORY_WRITE_REQUEST {
    ULONG ProcessId;        // 目标进程 ID
    PVOID BaseAddress;      // 写入的基地址
    SIZE_T Size;           // 写入大小
    // 数据紧跟在结构后面
} MEMORY_WRITE_REQUEST;
```

## 编译说明

### 环境要求
- Windows 10/11 开发环境
- Visual Studio 2019 或更高版本
- Windows Driver Kit (WDK) 10
- 管理员权限（用于驱动安装和测试）

### 编译步骤

1. **在 Visual Studio 中添加源文件**
   - 打开 `MyDriver.sln`
   - 右键点击项目 → 添加 → 现有项
   - 添加所有 `.c` 和 `.h` 文件到相应的过滤器中

2. **配置项目设置**
   - 确认项目类型为 "Driver"
   - 确认驱动类型为 "WDM"（项目已配置为 KMDF）
   - 选择目标平台（x64 推荐）

3. **编译驱动**
   - 选择 Release 配置
   - 构建解决方案 (Ctrl+Shift+B)

4. **编译测试程序**
   ```batch
   cl /EHsc TestApp.cpp /Fe:TestApp.exe
   ```

## 安装和测试

### 驱动安装
```batch
# 创建服务
sc create MyDriver type= kernel binPath= C:\path\to\MyDriver.sys

# 启动驱动
sc start MyDriver

# 停止驱动
sc stop MyDriver

# 删除服务
sc delete MyDriver
```

### 测试程序使用
```batch
# 以管理员身份运行
TestApp.exe
```

## 安全注意事项

### 受保护进程
驱动会拒绝访问以下系统进程：
- System (PID 4)
- csrss.exe
- winlogon.exe
- services.exe
- lsass.exe
- svchost.exe
- smss.exe
- wininit.exe

### 内存限制
- 最大单次操作: 1MB
- 最小操作大小: 1 字节
- 仅允许用户模式地址空间访问
- 地址必须按指针大小对齐

## 错误码

| 错误码 | 含义 |
|--------|------|
| `MYDRIVER_SUCCESS` | 操作成功 |
| `MYDRIVER_ERROR_INVALID_PARAMETER` | 无效参数 |
| `MYDRIVER_ERROR_PROCESS_NOT_FOUND` | 进程未找到 |
| `MYDRIVER_ERROR_ACCESS_DENIED` | 访问被拒绝 |
| `MYDRIVER_ERROR_INVALID_ADDRESS` | 无效地址 |
| `MYDRIVER_ERROR_BUFFER_TOO_SMALL` | 缓冲区太小 |
| `MYDRIVER_ERROR_MEMORY_ACCESS` | 内存访问错误 |

## 开发说明

### KMDF 特性
- 使用 KMDF 1.15+ 版本
- 事件驱动的编程模型
- 自动电源管理和 PnP 支持
- 内置的对象生命周期管理

### 代码质量
- 完整的错误处理和资源清理
- 详细的调试日志输出
- 内存操作统计和监控
- 遵循 Microsoft 内核编程最佳实践

## 许可证

本项目仅供学习和研究使用。请遵守当地法律法规，不得用于恶意目的。

## 技术支持

如有问题或建议，请检查：
1. Windows 事件查看器中的驱动日志
2. DebugView 中的调试输出
3. 确保以管理员权限运行
4. 验证目标进程是否存在且未受保护
