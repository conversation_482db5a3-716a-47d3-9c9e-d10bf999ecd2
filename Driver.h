#pragma once

//
// 驱动主头文件
// MyDriver - KMDF 进程内存读写驱动
//

#ifndef _DRIVER_H_
#define _DRIVER_H_

//
// 必要的系统头文件
//
#include <ntddk.h>
#include <wdf.h>
#include <ntstrsafe.h>

//
// 项目头文件
//
#include "Common.h"

//
// 编译器指令和预处理器定义
//
#pragma warning(push)
#pragma warning(disable:4201)  // 禁用非标准扩展警告

//
// KMDF 版本要求
//
KMDF_VERSION_MAJOR(1)
KMDF_VERSION_MINOR(15)

//
// 驱动上下文结构
//
typedef struct _DRIVER_CONTEXT {
    WDFDEVICE Device;           // 设备对象
    WDFQUEUE DefaultQueue;      // 默认 I/O 队列
    UNICODE_STRING DeviceName;  // 设备名称
    UNICODE_STRING SymbolicLink; // 符号链接
} DRIVER_CONTEXT, *PDRIVER_CONTEXT;

//
// 获取驱动上下文的宏
//
WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(DRIVER_CONTEXT, GetDriverContext)

//
// 函数声明
//

// 驱动入口点
DRIVER_INITIALIZE DriverEntry;

// 驱动回调函数
EVT_WDF_DRIVER_DEVICE_ADD EvtDriverDeviceAdd;
EVT_WDF_OBJECT_CONTEXT_CLEANUP EvtDriverContextCleanup;

// 设备相关函数（在 Device.h 中声明）
// I/O 处理函数（在 Device.h 中声明）
// 内存操作函数（在 Memory.h 中声明）

//
// 调试和日志宏
//
#if DBG
#define DebugPrint(format, ...) \
    DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_INFO_LEVEL, \
               "[MyDriver] " format "\n", ##__VA_ARGS__)
#else
#define DebugPrint(format, ...)
#endif

#define ErrorPrint(format, ...) \
    DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_ERROR_LEVEL, \
               "[MyDriver ERROR] " format "\n", ##__VA_ARGS__)

#define InfoPrint(format, ...) \
    DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_INFO_LEVEL, \
               "[MyDriver INFO] " format "\n", ##__VA_ARGS__)

//
// 内存标签定义
//
#define DRIVER_POOL_TAG    'vDyM'  // 'MyDv' 反向

//
// 实用宏定义
//
#define SAFE_FREE_POOL(ptr) \
    do { \
        if ((ptr) != NULL) { \
            ExFreePoolWithTag((ptr), DRIVER_POOL_TAG); \
            (ptr) = NULL; \
        } \
    } while (0)

#define IS_VALID_PROCESS_ID(pid) \
    ((pid) != INVALID_PROCESS_ID && (pid) != 0)

#define IS_VALID_SIZE(size) \
    ((size) >= MIN_MEMORY_SIZE && (size) <= MAX_MEMORY_SIZE)

#pragma warning(pop)

#endif // _DRIVER_H_
