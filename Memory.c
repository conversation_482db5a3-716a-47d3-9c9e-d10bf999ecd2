//
// 内存读写核心功能实现
// MyDriver - KMDF 进程内存读写驱动
//

#include "Memory.h"

//
// 受保护的系统进程名称列表
//
const WCHAR *ProtectedProcessNames[] = {
    L"System",
    L"csrss.exe",
    L"winlogon.exe",
    L"services.exe",
    L"lsass.exe",
    L"svchost.exe",
    L"smss.exe",
    L"wininit.exe"};

const ULONG ProtectedProcessCount = sizeof(ProtectedProcessNames) / sizeof(ProtectedProcessNames[0]);

//
// 全局内存操作统计
//
MEMORY_OPERATION_STATS g_MemoryStats = {0};
KSPIN_LOCK g_MemoryStatsLock;

#ifdef ALLOC_PRAGMA
#pragma alloc_text(PAGE, ValidateProcessId)
#pragma alloc_text(PAGE, ValidateMemoryAddress)
#pragma alloc_text(PAGE, CheckMemoryPermissions)
#pragma alloc_text(PAGE, IsSystemProcess)
#pragma alloc_text(PAGE, IsProtectedProcess)
#endif

//
// 初始化内存统计
//
VOID InitializeMemoryStats(VOID)
{
    RtlZeroMemory(&g_MemoryStats, sizeof(MEMORY_OPERATION_STATS));
    KeInitializeSpinLock(&g_MemoryStatsLock);
    InfoPrint("InitializeMemoryStats: 内存操作统计已初始化");
}

//
// 读取进程内存
//
NTSTATUS
ReadProcessMemory(
    _In_ ULONG ProcessId,
    _In_ PVOID BaseAddress,
    _Out_writes_bytes_(Size) PVOID Buffer,
    _In_ SIZE_T Size,
    _Out_opt_ PSIZE_T BytesRead)
{
    NTSTATUS status;
    PEPROCESS targetProcess = NULL;
    KAPC_STATE apcState;
    SIZE_T bytesTransferred = 0;
    BOOLEAN processAttached = FALSE;

    DebugPrint("ReadProcessMemory: PID=%lu, 地址=0x%p, 大小=%zu", ProcessId, BaseAddress, Size);

    //
    // 初始化返回值
    //
    if (BytesRead != NULL)
    {
        *BytesRead = 0;
    }

    //
    // 验证参数
    //
    if (Buffer == NULL || Size == 0 || Size > MAX_SINGLE_OPERATION_SIZE)
    {
        ErrorPrint("ReadProcessMemory: 无效参数");
        status = STATUS_INVALID_PARAMETER;
        goto Exit;
    }

    if (!IS_VALID_PROCESS_ID(ProcessId))
    {
        ErrorPrint("ReadProcessMemory: 无效进程 ID: %lu", ProcessId);
        status = STATUS_INVALID_PARAMETER;
        goto Exit;
    }

    //
    // 验证进程 ID 并获取进程对象
    //
    status = ValidateProcessId(ProcessId, &targetProcess);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("ReadProcessMemory: 进程验证失败，状态码: 0x%08X", status);
        goto Exit;
    }

    //
    // 检查是否为受保护进程
    //
    if (IsProtectedProcess(targetProcess))
    {
        ErrorPrint("ReadProcessMemory: 尝试访问受保护进程");
        status = STATUS_ACCESS_DENIED;
        goto Exit;
    }

    //
    // 验证内存地址
    //
    status = ValidateMemoryAddress(targetProcess, BaseAddress, Size, FALSE);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("ReadProcessMemory: 内存地址验证失败，状态码: 0x%08X", status);
        goto Exit;
    }

    //
    // 附加到目标进程地址空间
    //
    __try
    {
        KeStackAttachProcess(targetProcess, &apcState);
        processAttached = TRUE;

        //
        // 执行安全的内存复制
        //
        status = SafeMemoryCopy(
            targetProcess,
            Buffer,      // 目标缓冲区（当前进程）
            BaseAddress, // 源地址（目标进程）
            Size,
            FALSE, // 读取操作
            &bytesTransferred);
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        status = GetExceptionCode();
        ErrorPrint("ReadProcessMemory: 内存访问异常，状态码: 0x%08X", status);
    }

    //
    // 分离进程地址空间
    //
    if (processAttached)
    {
        KeUnstackDetachProcess(&apcState);
        processAttached = FALSE;
    }

    //
    // 设置返回值
    //
    if (BytesRead != NULL)
    {
        *BytesRead = bytesTransferred;
    }

    //
    // 更新统计信息
    //
    UpdateReadStats(bytesTransferred, NT_SUCCESS(status));

    if (NT_SUCCESS(status))
    {
        DebugPrint("ReadProcessMemory: 成功读取 %zu 字节", bytesTransferred);
    }

Exit:
    //
    // 清理资源
    //
    if (processAttached)
    {
        KeUnstackDetachProcess(&apcState);
    }

    if (targetProcess != NULL)
    {
        ObDereferenceObject(targetProcess);
    }

    return status;
}

//
// 写入进程内存
//
NTSTATUS
WriteProcessMemory(
    _In_ ULONG ProcessId,
    _In_ PVOID BaseAddress,
    _In_reads_bytes_(Size) PVOID Buffer,
    _In_ SIZE_T Size,
    _Out_opt_ PSIZE_T BytesWritten)
{
    NTSTATUS status;
    PEPROCESS targetProcess = NULL;
    KAPC_STATE apcState;
    SIZE_T bytesTransferred = 0;
    BOOLEAN processAttached = FALSE;

    DebugPrint("WriteProcessMemory: PID=%lu, 地址=0x%p, 大小=%zu", ProcessId, BaseAddress, Size);

    //
    // 初始化返回值
    //
    if (BytesWritten != NULL)
    {
        *BytesWritten = 0;
    }

    //
    // 验证参数
    //
    if (Buffer == NULL || Size == 0 || Size > MAX_SINGLE_OPERATION_SIZE)
    {
        ErrorPrint("WriteProcessMemory: 无效参数");
        status = STATUS_INVALID_PARAMETER;
        goto Exit;
    }

    if (!IS_VALID_PROCESS_ID(ProcessId))
    {
        ErrorPrint("WriteProcessMemory: 无效进程 ID: %lu", ProcessId);
        status = STATUS_INVALID_PARAMETER;
        goto Exit;
    }

    //
    // 验证进程 ID 并获取进程对象
    //
    status = ValidateProcessId(ProcessId, &targetProcess);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("WriteProcessMemory: 进程验证失败，状态码: 0x%08X", status);
        goto Exit;
    }

    //
    // 检查是否为受保护进程
    //
    if (IsProtectedProcess(targetProcess))
    {
        ErrorPrint("WriteProcessMemory: 尝试访问受保护进程");
        status = STATUS_ACCESS_DENIED;
        goto Exit;
    }

    //
    // 验证内存地址（写入操作需要更严格的检查）
    //
    status = ValidateMemoryAddress(targetProcess, BaseAddress, Size, TRUE);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("WriteProcessMemory: 内存地址验证失败，状态码: 0x%08X", status);
        goto Exit;
    }

    //
    // 附加到目标进程地址空间
    //
    __try
    {
        KeStackAttachProcess(targetProcess, &apcState);
        processAttached = TRUE;

        //
        // 执行安全的内存复制
        //
        status = SafeMemoryCopy(
            targetProcess,
            BaseAddress, // 目标地址（目标进程）
            Buffer,      // 源缓冲区（当前进程）
            Size,
            TRUE, // 写入操作
            &bytesTransferred);
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        status = GetExceptionCode();
        ErrorPrint("WriteProcessMemory: 内存访问异常，状态码: 0x%08X", status);
    }

    //
    // 分离进程地址空间
    //
    if (processAttached)
    {
        KeUnstackDetachProcess(&apcState);
        processAttached = FALSE;
    }

    //
    // 设置返回值
    //
    if (BytesWritten != NULL)
    {
        *BytesWritten = bytesTransferred;
    }

    //
    // 更新统计信息
    //
    UpdateWriteStats(bytesTransferred, NT_SUCCESS(status));

    if (NT_SUCCESS(status))
    {
        DebugPrint("WriteProcessMemory: 成功写入 %zu 字节", bytesTransferred);
    }

Exit:
    //
    // 清理资源
    //
    if (processAttached)
    {
        KeUnstackDetachProcess(&apcState);
    }

    if (targetProcess != NULL)
    {
        ObDereferenceObject(targetProcess);
    }

    return status;
}

//
// 验证进程 ID
//
NTSTATUS
ValidateProcessId(
    _In_ ULONG ProcessId,
    _Out_ PEPROCESS *Process)
{
    NTSTATUS status;
    PEPROCESS process = NULL;

    PAGED_CODE();

    *Process = NULL;

    //
    // 查找进程对象
    //
    status = PsLookupProcessByProcessId((HANDLE)(ULONG_PTR)ProcessId, &process);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("ValidateProcessId: 无法找到进程 ID %lu，状态码: 0x%08X", ProcessId, status);
        return status;
    }

    //
    // 检查进程是否仍然活跃
    //
    if (PsGetProcessExitStatus(process) != STATUS_PENDING)
    {
        ErrorPrint("ValidateProcessId: 进程 %lu 已退出", ProcessId);
        ObDereferenceObject(process);
        return STATUS_PROCESS_IS_TERMINATING;
    }

    //
    // 检查是否为系统进程
    //
    if (IsSystemProcess(process))
    {
        ErrorPrint("ValidateProcessId: 不允许访问系统进程 %lu", ProcessId);
        ObDereferenceObject(process);
        return STATUS_ACCESS_DENIED;
    }

    *Process = process;
    return STATUS_SUCCESS;
}

//
// 验证内存地址
//
NTSTATUS
ValidateMemoryAddress(
    _In_ PEPROCESS Process,
    _In_ PVOID BaseAddress,
    _In_ SIZE_T Size,
    _In_ BOOLEAN IsWrite)
{
    PAGED_CODE();

    UNREFERENCED_PARAMETER(Process);

    //
    // 检查地址是否为用户模式地址
    //
    if (!IsValidUserModeAddress(BaseAddress, Size))
    {
        ErrorPrint("ValidateMemoryAddress: 无效的用户模式地址 0x%p", BaseAddress);
        return STATUS_INVALID_PARAMETER;
    }

    //
    // 检查地址对齐
    //
    if ((ULONG_PTR)BaseAddress % MEMORY_ALIGNMENT != 0)
    {
        ErrorPrint("ValidateMemoryAddress: 地址未对齐 0x%p", BaseAddress);
        return STATUS_DATATYPE_MISALIGNMENT;
    }

    //
    // 检查大小限制
    //
    if (Size > MAX_SINGLE_OPERATION_SIZE)
    {
        ErrorPrint("ValidateMemoryAddress: 操作大小超出限制 %zu", Size);
        return STATUS_INVALID_PARAMETER;
    }

    //
    // 检查地址范围溢出
    //
    if ((ULONG_PTR)BaseAddress + Size < (ULONG_PTR)BaseAddress)
    {
        ErrorPrint("ValidateMemoryAddress: 地址范围溢出");
        return STATUS_INVALID_PARAMETER;
    }

    //
    // 对于写入操作，进行额外的权限检查
    //
    if (IsWrite)
    {
        // 这里可以添加更多的写入权限检查
        DebugPrint("ValidateMemoryAddress: 写入操作权限检查通过");
    }

    return STATUS_SUCCESS;
}
