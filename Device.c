//
// 设备和 I/O 队列实现
// MyDriver - KMDF 进程内存读写驱动
//

#include "Device.h"
#include "Memory.h"

#ifdef ALLOC_PRAGMA
#pragma alloc_text(PAGE, CreateDevice)
#pragma alloc_text(PAGE, CreateDeviceInterface)
#pragma alloc_text(PAGE, ConfigureQueue)
#pragma alloc_text(PAGE, EvtDeviceContextCleanup)
#pragma alloc_text(PAGE, ValidateRequestParameters)
#endif

//
// 创建设备对象
//
NTSTATUS
CreateDevice(
    _In_ WDFDRIVER Driver,
    _Inout_ PWDFDEVICE_INIT DeviceInit)
{
    NTSTATUS status;
    WDFDEVICE device;
    WDF_OBJECT_ATTRIBUTES deviceAttributes;
    PDEVICE_CONTEXT deviceContext;
    PDRIVER_CONTEXT driverContext;

    PAGED_CODE();

    InfoPrint("CreateDevice: 开始创建设备对象...");

    //
    // 设置设备对象属性
    //
    WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&deviceAttributes, DEVICE_CONTEXT);
    deviceAttributes.EvtCleanupCallback = EvtDeviceContextCleanup;

    //
    // 创建设备对象
    //
    status = WdfDeviceCreate(&DeviceInit, &deviceAttributes, &device);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("CreateDevice: WdfDeviceCreate 失败，状态码: 0x%08X", status);
        return status;
    }

    //
    // 获取设备上下文并初始化
    //
    deviceContext = GetDeviceContext(device);
    if (deviceContext == NULL)
    {
        ErrorPrint("CreateDevice: 无法获取设备上下文");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    RtlZeroMemory(deviceContext, sizeof(DEVICE_CONTEXT));
    deviceContext->Device = device;
    deviceContext->RequestCount = 0;
    KeInitializeSpinLock(&deviceContext->RequestLock);

    //
    // 更新驱动上下文
    //
    driverContext = GetDriverContext(Driver);
    if (driverContext != NULL)
    {
        driverContext->Device = device;
    }

    //
    // 创建设备接口
    //
    status = CreateDeviceInterface(device);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("CreateDevice: CreateDeviceInterface 失败，状态码: 0x%08X", status);
        return status;
    }

    //
    // 配置 I/O 队列
    //
    status = ConfigureQueue(device);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("CreateDevice: ConfigureQueue 失败，状态码: 0x%08X", status);
        return status;
    }

    InfoPrint("CreateDevice: 设备对象创建成功");
    return STATUS_SUCCESS;
}

//
// 创建设备接口
//
NTSTATUS
CreateDeviceInterface(
    _In_ WDFDEVICE Device)
{
    NTSTATUS status;
    UNICODE_STRING deviceName;
    UNICODE_STRING symbolicLink;

    PAGED_CODE();

    InfoPrint("CreateDeviceInterface: 开始创建设备接口...");

    //
    // 初始化设备名称
    //
    RtlInitUnicodeString(&deviceName, DEVICE_NAME);

    //
    // 创建设备名称
    //
    status = WdfDeviceCreateDeviceInterface(
        Device,
        NULL, // 使用默认接口类 GUID
        NULL  // 无引用字符串
    );

    if (!NT_SUCCESS(status))
    {
        ErrorPrint("CreateDeviceInterface: WdfDeviceCreateDeviceInterface 失败，状态码: 0x%08X", status);
        return status;
    }

    //
    // 初始化符号链接
    //
    RtlInitUnicodeString(&symbolicLink, SYMBOLIC_LINK_NAME);

    //
    // 创建符号链接
    //
    status = WdfDeviceCreateSymbolicLink(Device, &symbolicLink);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("CreateDeviceInterface: WdfDeviceCreateSymbolicLink 失败，状态码: 0x%08X", status);
        return status;
    }

    InfoPrint("CreateDeviceInterface: 设备接口创建成功");
    return STATUS_SUCCESS;
}

//
// 配置 I/O 队列
//
NTSTATUS
ConfigureQueue(
    _In_ WDFDEVICE Device)
{
    NTSTATUS status;
    WDF_IO_QUEUE_CONFIG queueConfig;
    WDFQUEUE queue;
    PDEVICE_CONTEXT deviceContext;

    PAGED_CODE();

    InfoPrint("ConfigureQueue: 开始配置 I/O 队列...");

    //
    // 获取设备上下文
    //
    deviceContext = GetDeviceContext(Device);
    if (deviceContext == NULL)
    {
        ErrorPrint("ConfigureQueue: 无法获取设备上下文");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    //
    // 初始化队列配置
    //
    WDF_IO_QUEUE_CONFIG_INIT_DEFAULT_QUEUE(&queueConfig, WdfIoQueueDispatchSequential);
    queueConfig.EvtIoDeviceControl = EvtIoDeviceControl;
    queueConfig.EvtIoStop = EvtIoStop;

    //
    // 创建 I/O 队列
    //
    status = WdfIoQueueCreate(
        Device,
        &queueConfig,
        WDF_NO_OBJECT_ATTRIBUTES,
        &queue);

    if (!NT_SUCCESS(status))
    {
        ErrorPrint("ConfigureQueue: WdfIoQueueCreate 失败，状态码: 0x%08X", status);
        return status;
    }

    //
    // 更新设备上下文
    //
    deviceContext->IoQueue = queue;

    InfoPrint("ConfigureQueue: I/O 队列配置成功");
    return STATUS_SUCCESS;
}

//
// 设备上下文清理回调函数
//
VOID EvtDeviceContextCleanup(
    _In_ WDFOBJECT DeviceObject)
{
    PDEVICE_CONTEXT deviceContext;

    PAGED_CODE();

    InfoPrint("EvtDeviceContextCleanup: 开始清理设备上下文...");

    //
    // 获取设备上下文
    //
    deviceContext = GetDeviceContext(DeviceObject);
    if (deviceContext != NULL)
    {
        InfoPrint("EvtDeviceContextCleanup: 当前请求计数: %d", deviceContext->RequestCount);

        //
        // 等待所有请求完成
        //
        while (deviceContext->RequestCount > 0)
        {
            KeDelayExecutionThread(KernelMode, FALSE, &(LARGE_INTEGER){.QuadPart = -10000}); // 1ms
        }
    }

    InfoPrint("EvtDeviceContextCleanup: 设备上下文清理完成");
}

//
// I/O 设备控制回调函数
//
VOID EvtIoDeviceControl(
    _In_ WDFQUEUE Queue,
    _In_ WDFREQUEST Request,
    _In_ size_t OutputBufferLength,
    _In_ size_t InputBufferLength,
    _In_ ULONG IoControlCode)
{
    NTSTATUS status;
    WDFDEVICE device;
    PDEVICE_CONTEXT deviceContext;

    UNREFERENCED_PARAMETER(Queue);

    //
    // 获取设备对象和上下文
    //
    device = WdfIoQueueGetDevice(Queue);
    deviceContext = GetDeviceContext(device);

    //
    // 增加请求计数
    //
    IncrementRequestCount(deviceContext);

    DebugPrint("EvtIoDeviceControl: 收到 IOCTL 请求，代码: 0x%08X", IoControlCode);

    //
    // 验证请求参数
    //
    status = ValidateRequestParameters(Request, IoControlCode, InputBufferLength, OutputBufferLength);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("EvtIoDeviceControl: 参数验证失败，状态码: 0x%08X", status);
        goto Exit;
    }

    //
    // 分发 IOCTL 命令
    //
    status = DispatchIoctl(Request, OutputBufferLength, InputBufferLength, IoControlCode);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("EvtIoDeviceControl: IOCTL 分发失败，状态码: 0x%08X", status);
    }

Exit:
    //
    // 完成请求
    //
    WdfRequestComplete(Request, status);

    //
    // 减少请求计数
    //
    DecrementRequestCount(deviceContext);
}

//
// I/O 队列停止回调函数
//
VOID EvtIoStop(
    _In_ WDFQUEUE Queue,
    _In_ WDFREQUEST Request,
    _In_ ULONG ActionFlags)
{
    UNREFERENCED_PARAMETER(Queue);
    UNREFERENCED_PARAMETER(ActionFlags);

    InfoPrint("EvtIoStop: 停止 I/O 请求");

    //
    // 确认请求停止
    //
    WdfRequestStopAcknowledge(Request, FALSE);
}

//
// IOCTL 命令分发器
//
NTSTATUS
DispatchIoctl(
    _In_ WDFREQUEST Request,
    _In_ size_t OutputBufferLength,
    _In_ size_t InputBufferLength,
    _In_ ULONG IoControlCode)
{
    NTSTATUS status;

    DebugPrint("DispatchIoctl: 分发 IOCTL 命令，代码: 0x%08X", IoControlCode);

    switch (IoControlCode)
    {
    case IOCTL_READ_PROCESS_MEMORY:
        DebugPrint("DispatchIoctl: 处理内存读取请求");
        status = HandleReadMemoryRequest(Request, InputBufferLength, OutputBufferLength);
        break;

    case IOCTL_WRITE_PROCESS_MEMORY:
        DebugPrint("DispatchIoctl: 处理内存写入请求");
        status = HandleWriteMemoryRequest(Request, InputBufferLength, OutputBufferLength);
        break;

    default:
        ErrorPrint("DispatchIoctl: 不支持的 IOCTL 命令: 0x%08X", IoControlCode);
        status = STATUS_INVALID_DEVICE_REQUEST;
        break;
    }

    return status;
}

//
// 处理内存读取请求
//
NTSTATUS
HandleReadMemoryRequest(
    _In_ WDFREQUEST Request,
    _In_ size_t InputBufferLength,
    _In_ size_t OutputBufferLength)
{
    NTSTATUS status;
    PMEMORY_READ_REQUEST readRequest;
    PVOID outputBuffer;
    size_t bytesReturned = 0;

    DebugPrint("HandleReadMemoryRequest: 开始处理内存读取请求");

    //
    // 验证输入缓冲区大小
    //
    if (InputBufferLength < sizeof(MEMORY_READ_REQUEST))
    {
        ErrorPrint("HandleReadMemoryRequest: 输入缓冲区太小");
        return STATUS_BUFFER_TOO_SMALL;
    }

    //
    // 获取输入缓冲区
    //
    status = WdfRequestRetrieveInputBuffer(Request, sizeof(MEMORY_READ_REQUEST), (PVOID *)&readRequest, NULL);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("HandleReadMemoryRequest: 无法获取输入缓冲区，状态码: 0x%08X", status);
        return status;
    }

    //
    // 验证输出缓冲区大小
    //
    if (OutputBufferLength < readRequest->Size)
    {
        ErrorPrint("HandleReadMemoryRequest: 输出缓冲区太小");
        return STATUS_BUFFER_TOO_SMALL;
    }

    //
    // 获取输出缓冲区
    //
    status = WdfRequestRetrieveOutputBuffer(Request, readRequest->Size, &outputBuffer, NULL);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("HandleReadMemoryRequest: 无法获取输出缓冲区，状态码: 0x%08X", status);
        return status;
    }

    //
    // 执行内存读取操作
    //
    status = ReadProcessMemory(
        readRequest->ProcessId,
        readRequest->BaseAddress,
        outputBuffer,
        readRequest->Size,
        &bytesReturned);

    if (NT_SUCCESS(status))
    {
        //
        // 设置返回的字节数
        //
        WdfRequestSetInformation(Request, bytesReturned);
        DebugPrint("HandleReadMemoryRequest: 成功读取 %zu 字节", bytesReturned);
    }
    else
    {
        ErrorPrint("HandleReadMemoryRequest: 内存读取失败，状态码: 0x%08X", status);
    }

    return status;
}

//
// 处理内存写入请求
//
NTSTATUS
HandleWriteMemoryRequest(
    _In_ WDFREQUEST Request,
    _In_ size_t InputBufferLength,
    _In_ size_t OutputBufferLength)
{
    NTSTATUS status;
    PMEMORY_WRITE_REQUEST writeRequest;
    PVOID inputBuffer;
    PVOID dataBuffer;
    size_t bytesWritten = 0;
    size_t requiredSize;

    UNREFERENCED_PARAMETER(OutputBufferLength);

    DebugPrint("HandleWriteMemoryRequest: 开始处理内存写入请求");

    //
    // 验证输入缓冲区最小大小
    //
    if (InputBufferLength < sizeof(MEMORY_WRITE_REQUEST))
    {
        ErrorPrint("HandleWriteMemoryRequest: 输入缓冲区太小");
        return STATUS_BUFFER_TOO_SMALL;
    }

    //
    // 获取输入缓冲区
    //
    status = WdfRequestRetrieveInputBuffer(Request, sizeof(MEMORY_WRITE_REQUEST), &inputBuffer, NULL);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("HandleWriteMemoryRequest: 无法获取输入缓冲区，状态码: 0x%08X", status);
        return status;
    }

    writeRequest = (PMEMORY_WRITE_REQUEST)inputBuffer;

    //
    // 验证输入缓冲区包含完整的数据
    //
    requiredSize = sizeof(MEMORY_WRITE_REQUEST) + writeRequest->Size;
    if (InputBufferLength < requiredSize)
    {
        ErrorPrint("HandleWriteMemoryRequest: 输入缓冲区不包含完整数据");
        return STATUS_BUFFER_TOO_SMALL;
    }

    //
    // 获取数据缓冲区（紧跟在请求结构后面）
    //
    dataBuffer = (PUCHAR)writeRequest + sizeof(MEMORY_WRITE_REQUEST);

    //
    // 执行内存写入操作
    //
    status = WriteProcessMemory(
        writeRequest->ProcessId,
        writeRequest->BaseAddress,
        dataBuffer,
        writeRequest->Size,
        &bytesWritten);

    if (NT_SUCCESS(status))
    {
        //
        // 设置返回的字节数
        //
        WdfRequestSetInformation(Request, bytesWritten);
        DebugPrint("HandleWriteMemoryRequest: 成功写入 %zu 字节", bytesWritten);
    }
    else
    {
        ErrorPrint("HandleWriteMemoryRequest: 内存写入失败，状态码: 0x%08X", status);
    }

    return status;
}

//
// 验证请求参数
//
NTSTATUS
ValidateRequestParameters(
    _In_ WDFREQUEST Request,
    _In_ ULONG IoControlCode,
    _In_ size_t InputBufferLength,
    _In_ size_t OutputBufferLength)
{
    UNREFERENCED_PARAMETER(Request);

    PAGED_CODE();

    DebugPrint("ValidateRequestParameters: 验证请求参数");

    //
    // 验证 IOCTL 命令码
    //
    switch (IoControlCode)
    {
    case IOCTL_READ_PROCESS_MEMORY:
        if (InputBufferLength < sizeof(MEMORY_READ_REQUEST))
        {
            ErrorPrint("ValidateRequestParameters: 读取请求输入缓冲区太小");
            return STATUS_BUFFER_TOO_SMALL;
        }
        if (OutputBufferLength == 0)
        {
            ErrorPrint("ValidateRequestParameters: 读取请求输出缓冲区为空");
            return STATUS_BUFFER_TOO_SMALL;
        }
        break;

    case IOCTL_WRITE_PROCESS_MEMORY:
        if (InputBufferLength < sizeof(MEMORY_WRITE_REQUEST))
        {
            ErrorPrint("ValidateRequestParameters: 写入请求输入缓冲区太小");
            return STATUS_BUFFER_TOO_SMALL;
        }
        break;

    default:
        ErrorPrint("ValidateRequestParameters: 无效的 IOCTL 命令码: 0x%08X", IoControlCode);
        return STATUS_INVALID_DEVICE_REQUEST;
    }

    DebugPrint("ValidateRequestParameters: 参数验证通过");
    return STATUS_SUCCESS;
}

//
// 增加请求计数
//
VOID IncrementRequestCount(
    _In_ PDEVICE_CONTEXT DeviceContext)
{
    KIRQL oldIrql;

    if (DeviceContext == NULL)
    {
        return;
    }

    KeAcquireSpinLock(&DeviceContext->RequestLock, &oldIrql);
    DeviceContext->RequestCount++;
    KeReleaseSpinLock(&DeviceContext->RequestLock, oldIrql);
}

//
// 减少请求计数
//
VOID DecrementRequestCount(
    _In_ PDEVICE_CONTEXT DeviceContext)
{
    KIRQL oldIrql;

    if (DeviceContext == NULL)
    {
        return;
    }

    KeAcquireSpinLock(&DeviceContext->RequestLock, &oldIrql);
    if (DeviceContext->RequestCount > 0)
    {
        DeviceContext->RequestCount--;
    }
    KeReleaseSpinLock(&DeviceContext->RequestLock, oldIrql);
}

//
// 获取当前请求计数
//
LONG GetRequestCount(
    _In_ PDEVICE_CONTEXT DeviceContext)
{
    KIRQL oldIrql;
    LONG count;

    if (DeviceContext == NULL)
    {
        return 0;
    }

    KeAcquireSpinLock(&DeviceContext->RequestLock, &oldIrql);
    count = DeviceContext->RequestCount;
    KeReleaseSpinLock(&DeviceContext->RequestLock, oldIrql);

    return count;
}
