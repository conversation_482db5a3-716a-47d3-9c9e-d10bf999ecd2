#pragma once

//
// 内存操作头文件
// MyDriver - KMDF 进程内存读写驱动
//

#ifndef _MEMORY_H_
#define _MEMORY_H_

#include "Driver.h"

//
// 内存操作函数声明
//

// 读取进程内存
NTSTATUS
ReadProcessMemory(
    _In_ ULONG ProcessId,
    _In_ PVOID BaseAddress,
    _Out_writes_bytes_(Size) PVOID Buffer,
    _In_ SIZE_T Size,
    _Out_opt_ PSIZE_T BytesRead
);

// 写入进程内存
NTSTATUS
WriteProcessMemory(
    _In_ ULONG ProcessId,
    _In_ PVOID BaseAddress,
    _In_reads_bytes_(Size) PVOID Buffer,
    _In_ SIZE_T Size,
    _Out_opt_ PSIZE_T BytesWritten
);

//
// 辅助函数声明
//

// 验证进程 ID
NTSTATUS
ValidateProcessId(
    _In_ ULONG ProcessId,
    _Out_ PEPROCESS* Process
);

// 验证内存地址
NTSTATUS
ValidateMemoryAddress(
    _In_ PEPROCESS Process,
    _In_ PVOID BaseAddress,
    _In_ SIZE_T Size,
    _In_ BOOLEAN IsWrite
);

// 安全的内存复制
NTSTATUS
SafeMemoryCopy(
    _In_ PEPROCESS TargetProcess,
    _In_ PVOID TargetAddress,
    _In_ PVOID SourceAddress,
    _In_ SIZE_T Size,
    _In_ BOOLEAN IsWrite,
    _Out_opt_ PSIZE_T BytesTransferred
);

// 检查内存区域权限
NTSTATUS
CheckMemoryPermissions(
    _In_ PEPROCESS Process,
    _In_ PVOID BaseAddress,
    _In_ SIZE_T Size,
    _In_ BOOLEAN RequireWrite
);

// 验证用户模式地址
BOOLEAN
IsValidUserModeAddress(
    _In_ PVOID Address,
    _In_ SIZE_T Size
);

// 检查是否为系统进程
BOOLEAN
IsSystemProcess(
    _In_ PEPROCESS Process
);

// 检查是否为受保护进程
BOOLEAN
IsProtectedProcess(
    _In_ PEPROCESS Process
);

//
// 内存操作配置常量
//
#define MEMORY_ALIGNMENT            sizeof(PVOID)
#define MAX_SINGLE_OPERATION_SIZE   (1024 * 1024)  // 1MB
#define MIN_USER_ADDRESS            0x10000         // 64KB
#define MAX_USER_ADDRESS            0x7FFFFFFF      // 2GB (32-bit) / 更大 (64-bit)

//
// 系统进程名称列表（用于保护）
//
extern const WCHAR* ProtectedProcessNames[];
extern const ULONG ProtectedProcessCount;

//
// 内存操作统计结构
//
typedef struct _MEMORY_OPERATION_STATS {
    ULONG64 TotalReadOperations;
    ULONG64 TotalWriteOperations;
    ULONG64 TotalBytesRead;
    ULONG64 TotalBytesWritten;
    ULONG64 FailedOperations;
    LARGE_INTEGER LastOperationTime;
} MEMORY_OPERATION_STATS, *PMEMORY_OPERATION_STATS;

//
// 全局统计变量
//
extern MEMORY_OPERATION_STATS g_MemoryStats;
extern KSPIN_LOCK g_MemoryStatsLock;

//
// 统计函数声明
//
VOID
InitializeMemoryStats(VOID);

VOID
UpdateReadStats(
    _In_ SIZE_T BytesRead,
    _In_ BOOLEAN Success
);

VOID
UpdateWriteStats(
    _In_ SIZE_T BytesWritten,
    _In_ BOOLEAN Success
);

VOID
GetMemoryStats(
    _Out_ PMEMORY_OPERATION_STATS Stats
);

#endif // _MEMORY_H_
