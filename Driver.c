//
// 驱动入口点实现
// MyDriver - KMDF 进程内存读写驱动
//

#include "Driver.h"
#include "Device.h"

#ifdef ALLOC_PRAGMA
#pragma alloc_text(INIT, DriverEntry)
#pragma alloc_text(PAGE, EvtDriverDeviceAdd)
#pragma alloc_text(PAGE, EvtDriverContextCleanup)
#endif

//
// 驱动入口点
//
NTSTATUS
DriverEntry(
    _In_ PDRIVER_OBJECT DriverObject,
    _In_ PUNICODE_STRING RegistryPath)
{
    NTSTATUS status;
    WDF_DRIVER_CONFIG config;
    WDF_OBJECT_ATTRIBUTES attributes;
    WDFDRIVER driver;

    UNREFERENCED_PARAMETER(RegistryPath);

    InfoPrint("DriverEntry: MyDriver KMDF 进程内存读写驱动正在加载...");

    //
    // 初始化驱动配置结构
    //
    WDF_DRIVER_CONFIG_INIT(&config, EvtDriverDeviceAdd);

    //
    // 设置驱动对象属性
    //
    WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&attributes, DRIVER_CONTEXT);
    attributes.EvtCleanupCallback = EvtDriverContextCleanup;

    //
    // 创建 KMDF 驱动对象
    //
    status = WdfDriverCreate(
        DriverObject,
        RegistryPath,
        &attributes,
        &config,
        &driver);

    if (!NT_SUCCESS(status))
    {
        ErrorPrint("DriverEntry: WdfDriverCreate 失败，状态码: 0x%08X", status);
        return status;
    }

    InfoPrint("DriverEntry: 驱动对象创建成功");

    //
    // 初始化驱动上下文
    //
    PDRIVER_CONTEXT driverContext = GetDriverContext(driver);
    if (driverContext != NULL)
    {
        RtlZeroMemory(driverContext, sizeof(DRIVER_CONTEXT));
        InfoPrint("DriverEntry: 驱动上下文初始化完成");
    }

    //
    // 初始化内存操作统计
    //
    InitializeMemoryStats();

    InfoPrint("DriverEntry: MyDriver 驱动加载完成，状态码: 0x%08X", status);
    return status;
}

//
// 设备添加回调函数
//
NTSTATUS
EvtDriverDeviceAdd(
    _In_ WDFDRIVER Driver,
    _Inout_ PWDFDEVICE_INIT DeviceInit)
{
    NTSTATUS status;
    PDRIVER_CONTEXT driverContext;

    UNREFERENCED_PARAMETER(Driver);
    PAGED_CODE();

    InfoPrint("EvtDriverDeviceAdd: 开始添加设备...");

    //
    // 获取驱动上下文
    //
    driverContext = GetDriverContext(Driver);
    if (driverContext == NULL)
    {
        ErrorPrint("EvtDriverDeviceAdd: 无法获取驱动上下文");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    //
    // 设置设备初始化参数
    //
    WdfDeviceInitSetIoType(DeviceInit, WdfDeviceIoBuffered);
    WdfDeviceInitSetExclusive(DeviceInit, FALSE);

    //
    // 创建设备对象
    //
    status = CreateDevice(Driver, DeviceInit);
    if (!NT_SUCCESS(status))
    {
        ErrorPrint("EvtDriverDeviceAdd: CreateDevice 失败，状态码: 0x%08X", status);
        return status;
    }

    InfoPrint("EvtDriverDeviceAdd: 设备添加完成，状态码: 0x%08X", status);
    return status;
}

//
// 驱动上下文清理回调函数
//
VOID EvtDriverContextCleanup(
    _In_ WDFOBJECT DriverObject)
{
    PDRIVER_CONTEXT driverContext;

    PAGED_CODE();

    InfoPrint("EvtDriverContextCleanup: 开始清理驱动上下文...");

    //
    // 获取驱动上下文
    //
    driverContext = GetDriverContext(DriverObject);
    if (driverContext != NULL)
    {
        //
        // 清理符号链接
        //
        if (driverContext->SymbolicLink.Buffer != NULL)
        {
            RtlFreeUnicodeString(&driverContext->SymbolicLink);
            InfoPrint("EvtDriverContextCleanup: 符号链接已清理");
        }

        //
        // 清理设备名称
        //
        if (driverContext->DeviceName.Buffer != NULL)
        {
            RtlFreeUnicodeString(&driverContext->DeviceName);
            InfoPrint("EvtDriverContextCleanup: 设备名称已清理");
        }
    }

    InfoPrint("EvtDriverContextCleanup: 驱动上下文清理完成");
}
