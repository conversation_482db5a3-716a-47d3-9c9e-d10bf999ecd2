#pragma once

//
// 设备管理头文件
// MyDriver - KMDF 进程内存读写驱动
//

#ifndef _DEVICE_H_
#define _DEVICE_H_

#include "Driver.h"

//
// 设备上下文结构
//
typedef struct _DEVICE_CONTEXT {
    WDFDEVICE Device;           // 设备对象引用
    WDFQUEUE IoQueue;           // I/O 队列
    LONG RequestCount;          // 当前请求计数
    KSPIN_LOCK RequestLock;     // 请求计数保护锁
} DEVICE_CONTEXT, *PDEVICE_CONTEXT;

//
// 获取设备上下文的宏
//
WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(DEVICE_CONTEXT, GetDeviceContext)

//
// 设备相关回调函数声明
//

// 设备添加回调
EVT_WDF_DRIVER_DEVICE_ADD EvtDriverDeviceAdd;

// 设备上下文清理回调
EVT_WDF_OBJECT_CONTEXT_CLEANUP EvtDeviceContextCleanup;

//
// I/O 队列相关回调函数声明
//

// I/O 设备控制回调
EVT_WDF_IO_QUEUE_IO_DEVICE_CONTROL EvtIoDeviceControl;

// I/O 队列停止回调
EVT_WDF_IO_QUEUE_IO_STOP EvtIoStop;

//
// 设备管理函数声明
//

// 创建设备对象
NTSTATUS
CreateDevice(
    _In_ WDFDRIVER Driver,
    _Inout_ PWDFDEVICE_INIT DeviceInit
);

// 创建设备接口
NTSTATUS
CreateDeviceInterface(
    _In_ WDFDEVICE Device
);

// 配置 I/O 队列
NTSTATUS
ConfigureQueue(
    _In_ WDFDEVICE Device
);

//
// I/O 处理函数声明
//

// IOCTL 命令分发器
NTSTATUS
DispatchIoctl(
    _In_ WDFREQUEST Request,
    _In_ size_t OutputBufferLength,
    _In_ size_t InputBufferLength,
    _In_ ULONG IoControlCode
);

// 处理内存读取请求
NTSTATUS
HandleReadMemoryRequest(
    _In_ WDFREQUEST Request,
    _In_ size_t InputBufferLength,
    _In_ size_t OutputBufferLength
);

// 处理内存写入请求
NTSTATUS
HandleWriteMemoryRequest(
    _In_ WDFREQUEST Request,
    _In_ size_t InputBufferLength,
    _In_ size_t OutputBufferLength
);

//
// 辅助函数声明
//

// 验证请求参数
NTSTATUS
ValidateRequestParameters(
    _In_ WDFREQUEST Request,
    _In_ ULONG IoControlCode,
    _In_ size_t InputBufferLength,
    _In_ size_t OutputBufferLength
);

// 增加请求计数
VOID
IncrementRequestCount(
    _In_ PDEVICE_CONTEXT DeviceContext
);

// 减少请求计数
VOID
DecrementRequestCount(
    _In_ PDEVICE_CONTEXT DeviceContext
);

// 获取当前请求计数
LONG
GetRequestCount(
    _In_ PDEVICE_CONTEXT DeviceContext
);

#endif // _DEVICE_H_
